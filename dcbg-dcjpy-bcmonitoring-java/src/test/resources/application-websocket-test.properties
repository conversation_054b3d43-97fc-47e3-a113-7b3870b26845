# WebSocket Test Configuration for BCMonitoring
# This configuration file provides WebSocket-specific test properties
# similar to the bcclient repository's test configuration approach

# WebSocket Connection Configuration
bcmonitoring.websocket.uri.host=${WEBSOCKET_URI_HOST:localhost}
bcmonitoring.websocket.uri.port=${WEBSOCKET_URI_PORT:8545}
bcmonitoring.websocket.secure=${WEBSOCKET_SECURE:false}

# WebSocket Connection Pool Configuration
bcmonitoring.websocket.pool.main.enabled=${MAIN_WEBSOCKET_ENABLED:true}
bcmonitoring.websocket.pool.sub.enabled=${SUB_WEBSOCKET_ENABLED:false}
bcmonitoring.websocket.pool.main.host=${MAIN_WEBSOCKET_HOST:localhost}
bcmonitoring.websocket.pool.main.port=${MAIN_WEBSOCKET_PORT:8545}
bcmonitoring.websocket.pool.sub.host=${SUB_WEBSOCKET_HOST:localhost}
bcmonitoring.websocket.pool.sub.port=${SUB_WEBSOCKET_PORT:8546}

# WebSocket Reconnection Configuration
bcmonitoring.websocket.reconnect.enabled=${WEBSOCKET_RECONNECT_ENABLED:true}
bcmonitoring.websocket.reconnect.maxAttempts=${WEBSOCKET_RECONNECT_MAX_ATTEMPTS:5}
bcmonitoring.websocket.reconnect.delayMs=${WEBSOCKET_RECONNECT_DELAY_MS:3}
bcmonitoring.websocket.reconnect.backoffMultiplier=${WEBSOCKET_RECONNECT_BACKOFF:1.0}

# WebSocket Timeout Configuration
bcmonitoring.websocket.timeout.connection=${WEBSOCKET_CONNECTION_TIMEOUT:10000}
bcmonitoring.websocket.timeout.read=${WEBSOCKET_READ_TIMEOUT:30000}
bcmonitoring.websocket.timeout.write=${WEBSOCKET_WRITE_TIMEOUT:30000}

# WebSocket Subscription Configuration
bcmonitoring.subscription.checkInterval=${SUBSCRIPTION_CHECK_INTERVAL:3000}
bcmonitoring.subscription.allowableBlockTimestampDiffSec=${ALLOWABLE_BLOCK_TIMESTAMP_DIFF:60}
bcmonitoring.subscription.maxBlocksBehind=${MAX_BLOCKS_BEHIND:10}

# Test-specific WebSocket Configuration
# These properties are used specifically for testing scenarios
test.websocket.mock.enabled=${TEST_WEBSOCKET_MOCK_ENABLED:true}
test.websocket.mock.simulateFailure=${TEST_WEBSOCKET_SIMULATE_FAILURE:false}
test.websocket.mock.simulateHandshakeError=${TEST_WEBSOCKET_SIMULATE_HANDSHAKE_ERROR:false}
test.websocket.mock.maxFailureAttempts=${TEST_WEBSOCKET_MAX_FAILURE_ATTEMPTS:3}
test.websocket.mock.intermittentFailure=${TEST_WEBSOCKET_INTERMITTENT_FAILURE:false}

# Blockchain Network Configuration for Testing
test.blockchain.network.id=${TEST_NETWORK_ID:3000}
test.blockchain.network.name=${TEST_NETWORK_NAME:test}
test.blockchain.gas.limit=${TEST_GAS_LIMIT:6721975}
test.blockchain.gas.price=${TEST_GAS_PRICE:20000000000}

# Test Environment Configuration
test.environment.localstack.enabled=${TEST_LOCALSTACK_ENABLED:true}
test.environment.testcontainers.enabled=${TEST_TESTCONTAINERS_ENABLED:true}
test.environment.cleanup.enabled=${TEST_CLEANUP_ENABLED:true}

# Logging Configuration for WebSocket Tests
logging.level.com.decurret_dcp.dcjpy.bcmonitoring.websocket=DEBUG
logging.level.org.web3j.protocol.websocket=DEBUG
logging.level.adhoc.helper=DEBUG
logging.level.adhoc.mock=DEBUG

# Spring Test Configuration
spring.main.lazy-initialization=true
spring.main.allow-bean-definition-overriding=true
spring.test.context.cache.maxSize=10

# Disable actual blockchain connections in tests
bcmonitoring.blockchain.enabled=${BLOCKCHAIN_ENABLED:false}
bcmonitoring.monitoring.enabled=${MONITORING_ENABLED:false}
bcmonitoring.scheduler.enabled=${SCHEDULER_ENABLED:false}
