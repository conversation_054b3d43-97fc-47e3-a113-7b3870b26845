# Test configuration for bcmonitoring-java (similar to bcclient test properties)
spring.application.name=Bcmonitoring-Test

# Server configuration
server.port=0

# WebSocket configuration for testing
websocket.uri.host=${WEBSOCKET_URI_HOST:localhost}
websocket.uri.port=${WEBSOCKET_URI_PORT:8545}

# Subscription configuration
subscription.check-interval=${SUBSCRIPTION_CHECK_INTERVAL:100}
subscription.allowable-block-timestamp-diff-sec=${ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC:60}

# AWS configuration for testing
aws.region=${AWS_REGION:ap-northeast-1}
aws.access-key-id=${AWS_ACCESS_KEY:test}
aws.secret-access-key=${AWS_SECRET_KEY:test}
aws.dynamodb.region=${DYNAMODB_REGION:ap-northeast-1}
aws.dynamodb.table-prefix=${DYNAMODB_TABLE_PREFIX:test}
aws.dynamodb.endpoint=${DYNAMODB_ENDPOINT:http://localhost:4566}
aws.s3.bucket-name=${S3_BUCKET_NAME:test-bucket}
aws.s3.region=${S3_REGION:ap-northeast-1}
aws.dynamodb.events-table-name=${EVENTS_TABLE_NAME:test-events}
aws.dynamodb.block-height-table-name=${BLOCK_HEIGHT_TABLE_NAME:test-block-height}

# LocalStack configuration for testing
local-stack.end-point=${LOCALSTACK_ENDPOINT:http://localhost:4566}
local-stack.region=${LOCALSTACK_REGION:ap-northeast-1}
local-stack.access-key=${LOCALSTACK_ACCESS_KEY:test}
local-stack.secret-key=${LOCALSTACK_SECRET_KEY:test}

# Environment configuration
env=${ENV:test}
abi-format=${ABI_FORMAT:hardhat}

# Test settings
spring.main.allow-bean-definition-overriding=true
logging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG
