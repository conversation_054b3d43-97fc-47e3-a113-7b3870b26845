package com.decurret_dcp.dcjpy.bcmonitoring.util

import org.web3j.protocol.websocket.WebSocketService
import spock.lang.Specification

/**
 * Unit tests for WebSocketUtil (similar to bcclient's WebSocketUtilSpec)
 * Tests WebSocket URI generation and service creation patterns
 */
class WebSocketUtilSpec extends Specification {

    def "GenerateWebSocketUri: ホストとポートの指定で正常に暗号化用のURIが生成されること"() {
        when:
        URI uri = WebSocketUtil.generateWebSocketUri(host, port, true)

        then:
        uri.toString() == expected

        where:
        host        | port   || expected
        "localhost" | "8546" || "wss://localhost:8546"
        "127.0.0.1" | "8546" || "wss://127.0.0.1:8546"
    }

    def "GenerateWebSocketUri: ホストとポートの指定で正常に非暗号化用のURIが生成されること"() {
        when:
        URI uri = WebSocketUtil.generateWebSocketUri(host, port, false)

        then:
        uri.toString() == expected

        where:
        host        | port   || expected
        "localhost" | "8546" || "ws://localhost:8546"
        "127.0.0.1" | "8546" || "ws://127.0.0.1:8546"
    }

    def "GenerateWebSocketUri: 不正なホストとポートを指定された場合にRuntimeExceptionが発生すること"() {
        when:
        WebSocketUtil.generateWebSocketUri(host, port, false)

        then:
        thrown(expected)

        where:
        host        | port   || expected
        " "         | "8546" || RuntimeException
        "localhost" | " "    || RuntimeException
    }

    def "GenerateWebSocketService(uri): 無効なURIへの接続が失敗しRuntimeExceptionが発生すること"() {
        when:
        URI uri = WebSocketUtil.generateWebSocketUri("localhost", "8001", false)
        WebSocketService webSocketService = WebSocketUtil.generateWebSocketService(uri)

        then:
        thrown(RuntimeException)

        cleanup:
        if (webSocketService != null) {
            webSocketService.close()
        }
    }

    def "GenerateWebSocketService(host, port, useSecureConnection): 無効なURIへの接続が失敗しRuntimeExceptionが発生すること"() {
        when:
        WebSocketService webSocketService = WebSocketUtil.generateWebSocketService("localhost", "8001", false)

        then:
        thrown(RuntimeException)

        cleanup:
        if (webSocketService != null) {
            webSocketService.close()
        }
    }

    def "GenerateWebSocketService(wsEndpoint): WebSocket URI文字列から正常にサービスが生成されること"() {
        when:
        String wsEndpoint = "ws://localhost:8001"
        WebSocketService webSocketService = WebSocketUtil.generateWebSocketService(wsEndpoint)

        then:
        thrown(RuntimeException) // Connection will fail but service creation should work

        cleanup:
        if (webSocketService != null) {
            webSocketService.close()
        }
    }

    def "GenerateWebSocketService(wsEndpoint): 不正なWebSocket URI文字列でRuntimeExceptionが発生すること"() {
        when:
        String wsEndpoint = "invalid-uri"
        WebSocketUtil.generateWebSocketService(wsEndpoint)

        then:
        thrown(RuntimeException)
    }
}
