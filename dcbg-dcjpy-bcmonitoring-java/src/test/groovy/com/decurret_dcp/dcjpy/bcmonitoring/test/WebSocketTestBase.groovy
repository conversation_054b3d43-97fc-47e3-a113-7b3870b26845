package com.decurret_dcp.dcjpy.bcmonitoring.test

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.util.WebSocketUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ActiveProfiles
import org.web3j.protocol.Web3j
import org.web3j.protocol.websocket.WebSocketService
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.notNull

/**
 * Base class for WebSocket testing (similar to bcclient's IntegrationTestBase)
 * Provides common WebSocket mocking setup and utilities
 */
@ActiveProfiles("test")
abstract class WebSocketTestBase extends Specification {

    protected static MockedStatic<WebSocketUtil> mockedWebSocketUtil
    protected static BcmonitoringConfigurationProperties testProperties

    @MockBean
    protected Web3jConfig web3jConfig

    def setupSpec() {
        setupWebSocketMocking()
    }

    def cleanupSpec() {
        if (mockedWebSocketUtil != null) {
            mockedWebSocketUtil.close()
        }
    }

    /**
     * Sets up WebSocket mocking similar to bcclient's approach
     */
    protected static void setupWebSocketMocking() {
        // Create mock WebSocket services
        WebSocketService mockService1 = Mockito.mock(WebSocketService.class)
        WebSocketService mockService2 = Mockito.mock(WebSocketService.class)

        // Setup test properties
        testProperties = createTestProperties()

        // Mock static WebSocketUtil methods
        mockedWebSocketUtil = Mockito.mockStatic(WebSocketUtil.class)
        mockedWebSocketUtil.when(WebSocketUtil.generateWebSocketService(
                (String) notNull(), (String) notNull(), (boolean) notNull()))
                .thenReturn(mockService1)
                .thenReturn(mockService2)

        // Mock string-based WebSocket service creation
        mockedWebSocketUtil.when(WebSocketUtil.generateWebSocketService((String) notNull()))
                .thenReturn(mockService1)
    }

    /**
     * Creates test properties with WebSocket configuration
     */
    protected static BcmonitoringConfigurationProperties createTestProperties() {
        def properties = new BcmonitoringConfigurationProperties()
        
        // Setup WebSocket configuration
        def websocket = new BcmonitoringConfigurationProperties.Websocket()
        def uri = new BcmonitoringConfigurationProperties.Websocket.Uri()
        uri.setHost("localhost")
        uri.setPort("8545")
        websocket.setUri(uri)
        properties.setWebsocket(websocket)

        // Setup subscription configuration
        def subscription = new BcmonitoringConfigurationProperties.Subscription()
        subscription.setCheckInterval("100")
        subscription.setAllowableBlockTimestampDiffSec("60")
        properties.setSubscription(subscription)

        // Setup AWS configuration
        def aws = new BcmonitoringConfigurationProperties.Aws()
        def dynamodb = new BcmonitoringConfigurationProperties.Aws.Dynamodb()
        dynamodb.setEventsTableName("test-events")
        dynamodb.setBlockHeightTableName("test-block-height")
        dynamodb.setTablePrefix("test")
        aws.setDynamodb(dynamodb)
        
        def s3 = new BcmonitoringConfigurationProperties.Aws.S3()
        s3.setBucketName("test-bucket")
        aws.setS3(s3)
        properties.setAws(aws)

        return properties
    }

    /**
     * Creates a mock Web3j instance for testing
     */
    protected Web3j createMockWeb3j() {
        return Mockito.mock(Web3j.class)
    }

    /**
     * Creates a mock Web3jConfig that returns the provided Web3j instance
     */
    protected Web3jConfig createMockWeb3jConfig(Web3j mockWeb3j) {
        Web3jConfig mockConfig = Mockito.mock(Web3jConfig.class)
        Mockito.when(mockConfig.getWeb3j()).thenReturn(mockWeb3j)
        Mockito.when(mockConfig.createWebSocketWeb3j()).thenReturn(mockWeb3j)
        return mockConfig
    }

    @TestConfiguration
    static class WebSocketTestConfiguration {
        
        @Bean
        BcmonitoringConfigurationProperties bcmonitoringConfigurationProperties() {
            return testProperties ?: createTestProperties()
        }
    }
}
