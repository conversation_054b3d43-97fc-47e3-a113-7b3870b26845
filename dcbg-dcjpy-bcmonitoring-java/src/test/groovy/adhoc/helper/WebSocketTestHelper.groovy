package adhoc.helper

import org.mockito.MockedStatic
import org.mockito.Mockito
import org.web3j.protocol.Web3j
import org.web3j.protocol.websocket.WebSocketService
import org.web3j.protocol.websocket.WebSocketClient
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlockNumber
import org.web3j.protocol.websocket.WebSocketHandshakeException
import io.reactivex.Flowable
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.*

/**
 * WebSocket Test Helper for BCMonitoring
 * 
 * This helper class provides WebSocket mocking utilities similar to the bcclient repository's
 * WebSocket testing patterns. It supports:
 * 1. Static method mocking for WebSocket service creation
 * 2. Connection pool mocking
 * 3. Error scenario simulation
 * 4. Reconnection logic testing
 */
class WebSocketTestHelper {

    // Static mocks for WebSocket utilities
    private MockedStatic<Web3j> mockedWeb3j
    private MockedStatic<WebSocketService> mockedWebSocketService
    
    // Mock WebSocket services for different scenarios
    private WebSocketService mockWebSocketService1
    private WebSocketService mockWebSocketService2
    private WebSocketService mockFailingWebSocketService
    
    // Mock Web3j instances
    private Web3j mockWeb3j1
    private Web3j mockWeb3j2
    private Web3j mockFailingWeb3j
    
    // Configuration for WebSocket behavior
    private boolean simulateConnectionFailure = false
    private boolean simulateHandshakeError = false
    private int connectionAttempts = 0
    private int maxConnectionAttempts = 3

    /**
     * Initialize WebSocket test helper with default mocks
     */
    void initialize() {
        setupWebSocketServiceMocks()
        setupWeb3jMocks()
        setupStaticMocks()
    }

    /**
     * Setup WebSocket service mocks similar to bcclient patterns
     */
    private void setupWebSocketServiceMocks() {
        mockWebSocketService1 = mock(WebSocketService.class)
        mockWebSocketService2 = mock(WebSocketService.class)
        mockFailingWebSocketService = mock(WebSocketService.class)
        
        // Configure successful WebSocket services
        configureMockWebSocketService(mockWebSocketService1, true)
        configureMockWebSocketService(mockWebSocketService2, true)
        
        // Configure failing WebSocket service
        configureMockWebSocketService(mockFailingWebSocketService, false)
    }

    /**
     * Configure a mock WebSocket service with specified behavior
     */
    void configureMockWebSocketService(WebSocketService mockService, boolean shouldSucceed) {
        if (shouldSucceed) {
            // Mock successful connection
            doNothing().when(mockService).connect()
            when(mockService.isConnected()).thenReturn(true)
            doNothing().when(mockService).close()
        } else {
            // Mock failing connection
            doThrow(new IOException("WebSocket connection failed"))
                .when(mockService).connect()
            when(mockService.isConnected()).thenReturn(false)
            doNothing().when(mockService).close()
        }
    }

    /**
     * Setup Web3j mocks with different behaviors
     */
    private void setupWeb3jMocks() {
        mockWeb3j1 = mock(Web3j.class)
        mockWeb3j2 = mock(Web3j.class)
        mockFailingWeb3j = mock(Web3j.class)
        
        // Configure successful Web3j instances
        configureWeb3jMock(mockWeb3j1, true)
        configureWeb3jMock(mockWeb3j2, true)
        
        // Configure failing Web3j instance
        configureWeb3jMock(mockFailingWeb3j, false)
    }

    /**
     * Configure a Web3j mock with specified behavior
     */
    void configureWeb3jMock(Web3j mockWeb3j, boolean shouldSucceed) {
        if (shouldSucceed) {
            // Mock successful blockchain operations
            def blockNumberResponse = new EthBlockNumber()
            blockNumberResponse.setResult("0x1234")
            def blockNumberRequest = mock(Request.class)
            when(blockNumberRequest.send()).thenReturn(blockNumberResponse)
            when(mockWeb3j.ethBlockNumber()).thenReturn(blockNumberRequest)
            
            // Mock successful block flowable
            when(mockWeb3j.blockFlowable(any())).thenReturn(Flowable.empty())
            
            // Mock successful shutdown
            doNothing().when(mockWeb3j).shutdown()
        } else {
            // Mock failing blockchain operations
            if (simulateHandshakeError) {
                when(mockWeb3j.blockFlowable(any()))
                    .thenThrow(new WebSocketHandshakeException("WebSocket handshake failed"))
            } else {
                when(mockWeb3j.blockFlowable(any()))
                    .thenThrow(new IOException("Connection failed"))
            }
            
            // Mock other operations to fail as well
            when(mockWeb3j.ethBlockNumber())
                .thenThrow(new IOException("Connection failed"))
        }
    }

    /**
     * Setup static mocks for Web3j and WebSocketService creation
     */
    private void setupStaticMocks() {
        // Mock Web3j.build() static method
        mockedWeb3j = mockStatic(Web3j.class)
        mockedWeb3j.when(() -> Web3j.build(any(WebSocketService.class)))
            .thenAnswer(invocation -> {
                WebSocketService service = invocation.getArgument(0)
                connectionAttempts++
                
                if (simulateConnectionFailure && connectionAttempts <= maxConnectionAttempts) {
                    return mockFailingWeb3j
                } else if (connectionAttempts % 2 == 0) {
                    return mockWeb3j2
                } else {
                    return mockWeb3j1
                }
            })
    }

    /**
     * Simulate WebSocket connection failure scenarios
     */
    void simulateConnectionFailure(boolean enable, int maxAttempts = 3) {
        this.simulateConnectionFailure = enable
        this.maxConnectionAttempts = maxAttempts
        this.connectionAttempts = 0
    }

    /**
     * Simulate WebSocket handshake error scenarios
     */
    void simulateHandshakeError(boolean enable) {
        this.simulateHandshakeError = enable
        configureWeb3jMock(mockFailingWeb3j, false)
    }

    /**
     * Reset connection attempt counter
     */
    void resetConnectionAttempts() {
        this.connectionAttempts = 0
    }

    /**
     * Get the number of connection attempts made
     */
    int getConnectionAttempts() {
        return connectionAttempts
    }

    /**
     * Get mock WebSocket service for testing
     */
    WebSocketService getMockWebSocketService(int index = 1) {
        switch (index) {
            case 1: return mockWebSocketService1
            case 2: return mockWebSocketService2
            default: return mockFailingWebSocketService
        }
    }

    /**
     * Get mock Web3j instance for testing
     */
    Web3j getMockWeb3j(int index = 1) {
        switch (index) {
            case 1: return mockWeb3j1
            case 2: return mockWeb3j2
            default: return mockFailingWeb3j
        }
    }

    /**
     * Cleanup all static mocks
     */
    void cleanup() {
        if (mockedWeb3j != null) {
            mockedWeb3j.close()
        }
        if (mockedWebSocketService != null) {
            mockedWebSocketService.close()
        }
    }

    /**
     * Verify WebSocket connection was attempted
     */
    void verifyConnectionAttempted(int expectedAttempts = 1) {
        assert connectionAttempts >= expectedAttempts : 
            "Expected at least ${expectedAttempts} connection attempts, but got ${connectionAttempts}"
    }

    /**
     * Verify WebSocket service was properly configured
     */
    void verifyWebSocketServiceConfiguration() {
        verify(mockWebSocketService1, atLeastOnce()).connect()
        verify(mockWebSocketService1, atLeastOnce()).isConnected()
    }
}
