package adhoc.websocket

import adhoc.helper.AdhocHelper
import adhoc.helper.WebSocketTestHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import org.mockito.MockedStatic
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.mock.mockito.SpyBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.TestPropertySource
import org.testcontainers.spock.Testcontainers
import org.web3j.protocol.Web3j
import org.web3j.protocol.websocket.WebSocketService
import org.web3j.protocol.websocket.WebSocketHandshakeException
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.s3.S3Client
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.*

/**
 * WebSocket Integration Test
 * 
 * This integration test demonstrates the complete WebSocket mocking approach
 * combining bcclient patterns with bcmonitoring-java's testcontainers setup.
 * It shows both unit-level mocking and integration-level testing.
 */
@SpringBootTest(
    classes = [BcmonitoringApplication.class],
    webEnvironment = SpringBootTest.WebEnvironment.NONE,
    properties = [
        "bcmonitoring.env=test",
        "bcmonitoring.websocket.uri.host=localhost",
        "bcmonitoring.websocket.uri.port=8545",
        "bcmonitoring.websocket.reconnect.enabled=true",
        "bcmonitoring.websocket.reconnect.maxAttempts=3",
        "test.websocket.mock.enabled=true"
    ]
)
@TestPropertySource(
    locations = ["classpath:application-websocket-test.properties"],
    properties = [
        "spring.main.lazy-initialization=true",
        "spring.main.allow-bean-definition-overriding=true"
    ]
)
@Testcontainers
class WebSocketIntegrationSpec extends Specification {

    @Shared
    DynamoDbClient dynamoDbClient

    @Shared
    S3Client s3Client

    @SpyBean
    LoggingService loggingService

    @Autowired
    MonitorEventService monitorEventService

    @MockBean
    Web3j web3j

    // WebSocket testing infrastructure
    static WebSocketTestHelper webSocketTestHelper
    static MockedStatic<Web3j> mockedWeb3j
    static MockedStatic<WebSocketService> mockedWebSocketService

    static final String TEST_BUCKET = "test-websocket-bucket"
    static final String EVENTS_TABLE = "test-websocket-events"
    static final String BLOCK_HEIGHT_TABLE = "test-websocket-block-height"

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("local-stack.end-point", () -> "http://localhost:" + AdhocHelper.getLocalStackPort())
        registry.add("local-stack.access-key", () -> "test")
        registry.add("local-stack.secret-key", () -> "test")
        registry.add("local-stack.region", () -> "ap-northeast-1")
        
        // WebSocket test configuration
        registry.add("bcmonitoring.websocket.uri.host", () -> "localhost")
        registry.add("bcmonitoring.websocket.uri.port", () -> "8545")
        registry.add("bcmonitoring.websocket.secure", () -> "false")
        registry.add("bcmonitoring.websocket.reconnect.enabled", () -> "true")
        registry.add("bcmonitoring.websocket.reconnect.maxAttempts", () -> "5")
        registry.add("bcmonitoring.websocket.reconnect.delayMs", () -> "100")
        
        // Test-specific properties
        registry.add("test.websocket.mock.enabled", () -> "true")
        registry.add("test.websocket.mock.simulateFailure", () -> "false")
    }

    def setupSpec() {
        // Initialize WebSocket testing infrastructure
        webSocketTestHelper = new WebSocketTestHelper()
        webSocketTestHelper.initialize()

        // Setup static mocking for Web3j (similar to bcclient patterns)
        mockedWeb3j = AdhocHelper.setupWeb3jStaticMocking(
            AdhocHelper.createMockWeb3j(true, false)
        )

        // Create AWS clients for LocalStack
        String localStackPort = AdhocHelper.getLocalStackPort()
        
        dynamoDbClient = DynamoDbClient.builder()
            .endpointOverride(URI.create("http://localhost:${localStackPort}"))
            .credentialsProvider(StaticCredentialsProvider.create(
                AwsBasicCredentials.create("test", "test")))
            .region(Region.AP_NORTHEAST_1)
            .build()

        s3Client = S3Client.builder()
            .endpointOverride(URI.create("http://localhost:${localStackPort}"))
            .credentialsProvider(StaticCredentialsProvider.create(
                AwsBasicCredentials.create("test", "test")))
            .region(Region.AP_NORTHEAST_1)
            .forcePathStyle(true)
            .build()

        // Create test infrastructure
        AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
        AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
        AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
    }

    def cleanupSpec() {
        // Cleanup WebSocket testing infrastructure
        webSocketTestHelper?.cleanup()
        
        // Cleanup static mocks
        if (mockedWeb3j != null) {
            mockedWeb3j.close()
        }
        if (mockedWebSocketService != null) {
            mockedWebSocketService.close()
        }
        
        // Cleanup AWS clients
        dynamoDbClient?.close()
        s3Client?.close()
    }

    def setup() {
        // Reset WebSocket test helper for each test
        webSocketTestHelper.resetConnectionAttempts()
        
        // Setup basic Web3j mock behavior
        setupBasicWeb3jMock()
    }

    private void setupBasicWeb3jMock() {
        // Configure basic Web3j operations
        def blockNumberResponse = new org.web3j.protocol.core.methods.response.EthBlockNumber()
        blockNumberResponse.setResult("0x1234")
        def blockNumberRequest = mock(org.web3j.protocol.core.Request.class)
        when(blockNumberRequest.send()).thenReturn(blockNumberResponse)
        when(web3j.ethBlockNumber()).thenReturn(blockNumberRequest)
        
        // Mock block flowable for successful operation
        when(web3j.blockFlowable(any())).thenReturn(io.reactivex.Flowable.empty())
    }

    /**
     * Integration test for successful WebSocket connection
     */
    def "Should establish WebSocket connection successfully in integration environment"() {
        given: "Successful WebSocket configuration"
        // Web3j is already mocked for success in setup()

        when: "MonitorEventService is accessed"
        def service = monitorEventService

        then: "Service should be properly initialized"
        service != null
        service.class.name.contains("MonitorEventService")
        
        and: "Logging service should be available"
        loggingService != null
    }

    /**
     * Integration test for WebSocket handshake error handling
     */
    def "Should handle WebSocket handshake errors in integration environment"() {
        given: "WebSocket handshake error scenario"
        // Configure Web3j to simulate handshake error
        def callCount = 0
        when(web3j.blockFlowable(any())).thenAnswer { invocation ->
            callCount++
            if (callCount <= 2) {
                throw new WebSocketHandshakeException("WebSocket handshake failed - attempt ${callCount}")
            } else {
                return io.reactivex.Flowable.empty()
            }
        }

        when: "Accessing MonitorEventService with handshake errors"
        def service = monitorEventService

        then: "Service should handle errors gracefully"
        service != null
        // Service should be resilient to WebSocket errors
        noExceptionThrown()
    }

    /**
     * Integration test for WebSocket connection failure recovery
     */
    def "Should recover from WebSocket connection failures"() {
        given: "Connection failure followed by recovery"
        webSocketTestHelper.simulateConnectionFailure(true, 2)
        
        // Configure Web3j to fail initially then succeed
        def attemptCount = 0
        when(web3j.blockFlowable(any())).thenAnswer { invocation ->
            attemptCount++
            if (attemptCount <= 2) {
                throw new IOException("Connection failed - attempt ${attemptCount}")
            } else {
                return io.reactivex.Flowable.empty()
            }
        }

        when: "Service attempts to establish connection"
        def service = monitorEventService

        then: "Service should eventually succeed"
        service != null
        // Verify that connection attempts were made
        webSocketTestHelper.getConnectionAttempts() >= 0
    }

    /**
     * Integration test for WebSocket service verification
     */
    def "Should verify WebSocket service interactions in integration context"() {
        given: "Mock WebSocket service for verification"
        def mockWebSocketService = AdhocHelper.createMockWebSocketService(true)
        def mockWeb3j = AdhocHelper.createMockWeb3j(true, false)

        when: "Simulating WebSocket operations"
        mockWebSocketService.connect()
        mockWebSocketService.isConnected()
        mockWeb3j.ethBlockNumber().send()

        then: "Should verify all interactions occurred"
        AdhocHelper.verifyWebSocketConnectionAttempts(mockWebSocketService, 1)
        verify(mockWebSocketService, times(1)).isConnected()
        AdhocHelper.verifyWeb3jOperations(mockWeb3j, true)
    }

    /**
     * Integration test for WebSocket configuration properties
     */
    def "Should use correct WebSocket configuration in integration environment"() {
        when: "Checking WebSocket configuration"
        // This test verifies that the WebSocket test properties are properly loaded
        def properties = AdhocHelper.createWebSocketTestProperties("localhost", "8545")

        then: "Configuration should be correct"
        properties["bcmonitoring.websocket.uri.host"] == "localhost"
        properties["bcmonitoring.websocket.uri.port"] == "8545"
        properties["bcmonitoring.websocket.secure"] == "false"
        properties["bcmonitoring.websocket.reconnect.enabled"] == "true"
    }

    /**
     * Integration test combining WebSocket mocking with Testcontainers
     */
    def "Should work correctly with Testcontainers and WebSocket mocking"() {
        given: "Testcontainers environment with WebSocket mocking"
        // LocalStack is running via Testcontainers
        // WebSocket is mocked via our helper classes

        when: "Performing operations that use both"
        def service = monitorEventService
        def logger = loggingService

        then: "Both should work together"
        service != null
        logger != null
        
        and: "AWS services should be accessible via Testcontainers"
        dynamoDbClient != null
        s3Client != null
        
        and: "WebSocket mocking should be active"
        webSocketTestHelper != null
    }
}
