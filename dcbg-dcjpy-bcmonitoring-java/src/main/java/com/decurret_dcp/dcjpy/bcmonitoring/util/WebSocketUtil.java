package com.decurret_dcp.dcjpy.bcmonitoring.util;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import org.springframework.lang.NonNull;
import org.web3j.protocol.websocket.WebSocketService;

/**
 * WebSocket関連の汎用クラス (similar to bcclient's WebSocketUtil)
 * Provides centralized WebSocket service creation for easier testing and mocking
 */
public class WebSocketUtil {

  private static final String WEB_SOCKET_URI_PREFIX = "ws://";
  private static final String WEB_SOCKET_SECURE_URI_PREFIX = "wss://";
  private static final String COLON = ":";

  /**
   * 指定したホストとポートからWebSocketServiceを生成する
   *
   * @param host ホスト
   * @param port ポート
   * @param useSecureConnection 暗号化通信を使用する
   * @return WebSocketService
   */
  @NonNull
  public static WebSocketService generateWebSocketService(
      @NonNull String host, @NonNull String port, @NonNull boolean useSecureConnection) {
    URI uri = generateWebSocketUri(host, port, useSecureConnection);
    return generateWebSocketService(uri);
  }

  /**
   * WebSocket用のURIを生成する
   *
   * @param host ホスト
   * @param port ポート
   * @param useSecureConnection 暗号化通信を使用する
   * @return URI WebSocketUri
   */
  @NonNull
  static URI generateWebSocketUri(
      @NonNull String host, @NonNull String port, @NonNull boolean useSecureConnection) {
    StringBuilder sb = new StringBuilder();
    sb.append(useSecureConnection ? WEB_SOCKET_SECURE_URI_PREFIX : WEB_SOCKET_URI_PREFIX);
    sb.append(host);
    sb.append(COLON);
    sb.append(port);

    try {
      return new URI(sb.toString());
    } catch (URISyntaxException uriExc) {
      throw new RuntimeException("Invalid websocket url", uriExc);
    }
  }

  /**
   * 指定したURIからWebSocketServiceを生成する
   *
   * @param uri WebSocket URI
   * @return WebSocketService
   */
  @NonNull
  static WebSocketService generateWebSocketService(@NonNull URI uri) {
    WebSocketService webSocketService = new WebSocketService(uri.toString(), true);
    try {
      webSocketService.connect();
    } catch (IOException ioExc) {
      webSocketService.close();
      throw new RuntimeException("Websocket connection failed", ioExc);
    }

    return webSocketService;
  }

  /**
   * WebSocket URI文字列を直接使用してWebSocketServiceを生成する
   * (for backward compatibility with existing code)
   *
   * @param wsEndpoint WebSocket endpoint URI string
   * @return WebSocketService
   */
  @NonNull
  public static WebSocketService generateWebSocketService(@NonNull String wsEndpoint) {
    try {
      URI uri = new URI(wsEndpoint);
      return generateWebSocketService(uri);
    } catch (URISyntaxException e) {
      throw new RuntimeException("Invalid websocket endpoint: " + wsEndpoint, e);
    }
  }
}
