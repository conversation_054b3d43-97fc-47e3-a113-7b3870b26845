# WebSocket Testing Implementation for BCMonitoring-Java

## Overview

This document describes the implementation of WebSocket mocking strategies in the bcmonitoring-java repository, based on the effective patterns discovered in the bcclient repository. The implementation provides comprehensive WebSocket testing capabilities while maintaining consistency with the existing testcontainers setup.

## Implementation Summary

### 1. WebSocket Testing Infrastructure

#### New Helper Classes Created:

1. **`WebSocketTestHelper.groovy`** - Main WebSocket testing utility
   - Provides static method mocking for WebSocket service creation
   - Manages mock Web3j instances with configurable behavior
   - Supports connection failure and handshake error simulation
   - Similar to bcclient's WebSocketUtil mocking patterns

2. **`WebSocketConnectionPoolMock.groovy`** - Connection pool simulation
   - Mimics bcclient's MainWebSocketConnectionPool and SubWebSocketConnectionPool
   - Provides controlled WebSocket connection management
   - Supports error scenarios and recovery testing
   - Thread-safe implementation with ReadWriteLock

3. **Enhanced `AdhocHelper.groovy`** - Extended with WebSocket utilities
   - Added WebSocket-specific helper methods
   - Provides mock creation and verification utilities
   - Maintains consistency with existing AdhocHelper patterns

#### New Configuration Files:

1. **`application-websocket-test.properties`** - WebSocket test configuration
   - Comprehensive WebSocket connection settings
   - Test-specific properties for mocking control
   - Similar to bcclient's test property structure

### 2. Testing Patterns Implemented

#### A. Static Method Mocking (Similar to BCClient)

```groovy
// Setup static mocking for Web3j.build() method
mockedWeb3j = AdhocHelper.setupWeb3jStaticMocking(
    AdhocHelper.createMockWeb3j(true, false)
)
```

This pattern intercepts WebSocket service creation at the static method level, similar to how bcclient mocks `WebSocketUtil.generateWebSocketService()`.

#### B. Spring Boot @MockBean Integration

```groovy
@MockBean
Web3j web3j

// Enhanced with WebSocket testing infrastructure
static WebSocketTestHelper webSocketTestHelper
static WebSocketConnectionPoolMock webSocketConnectionPool
```

Combines Spring Boot's @MockBean with our custom WebSocket mocking infrastructure.

#### C. Connection Pool Mocking

```groovy
// Create WebSocket connection pool mock
webSocketConnectionPool = new WebSocketConnectionPoolMock("localhost", "8545", false)

// Test connection replacement (similar to bcclient)
webSocketConnectionPool.createWebSocketConnection()
def connection1 = webSocketConnectionPool.getWebSocketConnection()
webSocketConnectionPool.createWebSocketConnection()
def connection2 = webSocketConnectionPool.getWebSocketConnection()

// Verify connections are different instances
assert connection1 != connection2
```

#### D. Error Scenario Testing

```groovy
// Simulate WebSocket handshake errors
webSocketConnectionPool.simulateHandshakeError(true, 3)

// Configure Web3j to throw WebSocketHandshakeException
def callCount = 0
web3j.blockFlowable(_) >> {
    callCount++
    if (callCount <= 3) {
        throw new WebSocketHandshakeException("WebSocket handshake failed - attempt ${callCount}")
    } else {
        return io.reactivex.Flowable.empty()
    }
}
```

### 3. Test File Structure

#### Enhanced Integration Tests:
- **`StartupServiceSpec.groovy`** - Enhanced with WebSocket mocking
- **`WebSocketIntegrationSpec.groovy`** - Comprehensive integration testing

#### New Unit Tests:
- **`WebSocketConnectionPoolSpec.groovy`** - Unit tests for WebSocket connection management

### 4. Key Improvements Over Original Implementation

#### Before (Original Implementation):
```groovy
// Simple Web3j mocking without connection pool simulation
@MockBean
Web3j web3j

// Basic error simulation
web3j.blockFlowable(_) >> {
    throw new WebSocketHandshakeException("WebSocket handshake failed")
}
```

#### After (Enhanced Implementation):
```groovy
// Comprehensive WebSocket testing infrastructure
static WebSocketTestHelper webSocketTestHelper
static WebSocketConnectionPoolMock webSocketConnectionPool
static MockedStatic<Web3j> mockedWeb3j

// Advanced error simulation with retry logic
webSocketConnectionPool.simulateHandshakeError(true, 3)
def callCount = 0
web3j.blockFlowable(_) >> {
    callCount++
    if (callCount <= 3) {
        throw new WebSocketHandshakeException("WebSocket handshake failed - attempt ${callCount}")
    } else {
        return io.reactivex.Flowable.empty()
    }
}
```

### 5. Benefits of the New Implementation

#### A. Improved Test Reliability
- Consistent WebSocket mocking across all tests
- Proper cleanup of static mocks prevents test interference
- Controlled error scenarios for better edge case testing

#### B. Enhanced Maintainability
- Centralized WebSocket testing utilities
- Reusable mock configurations
- Clear separation between unit and integration tests

#### C. Better Coverage
- Connection pool lifecycle testing
- Error handling and recovery scenarios
- WebSocket subscription management
- Mockito verification patterns

#### D. Consistency with BCClient Patterns
- Similar static method mocking approach
- Comparable connection pool testing patterns
- Consistent error simulation strategies
- Aligned verification methodologies

### 6. Usage Examples

#### Unit Testing:
```groovy
def "Should replace WebSocket connections when created multiple times"() {
    when:
    connectionPool.createWebSocketConnection()
    def connection1 = connectionPool.getWebSocketConnection()
    connectionPool.createWebSocketConnection()
    def connection2 = connectionPool.getWebSocketConnection()

    then:
    connection1 != connection2
    connectionPool.getConnectionAttempts() == 2
}
```

#### Integration Testing:
```groovy
def "Should handle WebSocket handshake errors in integration environment"() {
    given:
    webSocketConnectionPool.simulateHandshakeError(true, 3)
    
    when:
    def service = monitorEventService

    then:
    service != null
    noExceptionThrown()
}
```

#### Verification Testing:
```groovy
def "Should verify WebSocket service interactions"() {
    given:
    def mockService = AdhocHelper.createMockWebSocketService(true)

    when:
    mockService.connect()

    then:
    AdhocHelper.verifyWebSocketConnectionAttempts(mockService, 1)
}
```

### 7. Integration with Existing Infrastructure

The new WebSocket mocking implementation seamlessly integrates with:
- **Testcontainers**: LocalStack continues to provide AWS services
- **Spock Framework**: All tests use Spock's BDD syntax
- **Spring Boot Test**: @MockBean and @SpyBean annotations work as before
- **Existing AdhocHelper**: Extended rather than replaced

### 8. Future Enhancements

Potential areas for further improvement:
- WebSocket message content mocking
- Performance testing utilities
- Advanced subscription lifecycle testing
- Integration with monitoring metrics

## Conclusion

The enhanced WebSocket testing implementation provides a robust, maintainable, and comprehensive testing framework that combines the best practices from bcclient with bcmonitoring-java's existing infrastructure. It significantly improves test reliability and coverage while maintaining consistency with established patterns.
